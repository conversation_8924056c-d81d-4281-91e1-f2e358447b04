/**
 * 国际化日期时间格式化 Vue 插件
 * 将日期格式化方法注册为全局方法
 */

import dateI18nUtils from '@/utils/dateI18n';

const DateI18nPlugin = {
    install(Vue) {
        // 将所有日期格式化方法添加到 Vue 原型上
        Object.keys(dateI18nUtils).forEach(key => {
            if (typeof dateI18nUtils[key] === 'function' && key !== 'dateI18nMixin') {
                Vue.prototype[`$${key}`] = dateI18nUtils[key];
            }
        });

        // 添加便捷的格式化方法
        Vue.prototype.$dateFormat = {
            // 根据当前语言格式化日期
            date: (date) => dateI18nUtils.formatDateOnly(date),
            time: (date) => dateI18nUtils.formatTimeOnly(date),
            datetime: (date) => dateI18nUtils.formatDateTime(date),
            dateShort: (date) => dateI18nUtils.formatDateShort(date),
            timeShort: (date) => dateI18nUtils.formatTimeShort(date),
            monthDay: (date) => dateI18nUtils.formatMonthDay(date),
            yearMonth: (date) => dateI18nUtils.formatYearMonth(date),
            weekday: (date) => dateI18nUtils.getWeekday(date),
            timeAgo: (timestamp) => dateI18nUtils.formatTimeAgo(timestamp),
            smart: (date) => dateI18nUtils.formatDateSmart(date),
            
            // 自定义格式
            custom: (date, format) => dateI18nUtils.formatDate(date, format)
        };

        // 添加过滤器
        Vue.filter('dateFormat', dateI18nUtils.formatDateOnly);
        Vue.filter('timeFormat', dateI18nUtils.formatTimeOnly);
        Vue.filter('datetimeFormat', dateI18nUtils.formatDateTime);
        Vue.filter('dateShortFormat', dateI18nUtils.formatDateShort);
        Vue.filter('timeShortFormat', dateI18nUtils.formatTimeShort);
        Vue.filter('monthDayFormat', dateI18nUtils.formatMonthDay);
        Vue.filter('yearMonthFormat', dateI18nUtils.formatYearMonth);
        Vue.filter('weekdayFormat', dateI18nUtils.getWeekday);
        Vue.filter('timeAgoFormat', dateI18nUtils.formatTimeAgo);
        Vue.filter('dateSmartFormat', dateI18nUtils.formatDateSmart);
        Vue.filter('customDateFormat', (date, format) => dateI18nUtils.formatDate(date, format));
    }
};

export default DateI18nPlugin;
