// vue-i18n-extract 配置文件
// 用于扫描项目中需要国际化的文本

const VueI18nExtract = require('vue-i18n-extract');

// 配置选项
const config = {
    // Vue 文件路径
    vueFiles: './src/**/*.?(vue|js)',

    // 语言文件路径
    languageFiles: './src/locales/**/*.?(js|json)',

    // 输出选项
    output: {
    // 输出报告到文件
        file: './i18n-report.json',
        // 同时输出到控制台
        console: true
    },

    // 扫描选项
    options: {
    // 是否移除未使用的键
        remove: false,
        // 是否添加缺失的键
        add: false,
        // 是否显示详细信息
        ci: false,
        // 自定义正则表达式匹配模式
        patterns: [
            // 匹配 $t('key') 模式
            /\$t\s*\(\s*['"`]([^'"`]+)['"`]/g,
            // 匹配 this.$t('key') 模式
            /this\.\$t\s*\(\s*['"`]([^'"`]+)['"`]/g,
            // 匹配 i18n.t('key') 模式
            /i18n\.t\s*\(\s*['"`]([^'"`]+)['"`]/g
        ]
    }
};

// 执行扫描
async function scanI18n() {
    try {
        console.log('开始扫描国际化文本...');

        const report = VueI18nExtract.createI18nReport({
            vueFiles: config.vueFiles,
            languageFiles: config.languageFiles
        });

        // 输出统计信息
        console.log('\n=== 国际化扫描报告 ===');
        console.log(`未使用的翻译键: ${report.unusedKeys.length}`);
        console.log(`缺失的翻译键: ${report.missingKeys.length}`);

        // 详细报告
        if (report.missingKeys.length > 0) {
            console.log('\n缺失的翻译键:');
            report.missingKeys.forEach(key => {
                console.log(`- ${key.path}: ${key.key}`);
            });
        }

        if (report.unusedKeys.length > 0) {
            console.log('\n未使用的翻译键:');
            report.unusedKeys.forEach(key => {
                console.log(`- ${key.path}: ${key.key}`);
            });
        }

        // 保存报告到文件
        const fs = require('fs');
        fs.writeFileSync('./i18n-report.json', JSON.stringify(report, null, 2));
        console.log('\n报告已保存到 i18n-report.json');

    } catch (error) {
        console.error('扫描失败:', error);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    scanI18n();
}

module.exports = { scanI18n, config };
