/**
 * 国际化混入
 * 提供安全的翻译方法，避免 i18n 实例未初始化时的错误
 */
export default {
    methods: {
        /**
         * 安全的翻译方法
         * @param {string} key - 翻译键
         * @param {object} params - 翻译参数
         * @param {string} fallback - 备用文本
         * @returns {string} 翻译后的文本
         */
        $safeT(key, params = {}, fallback = '') {
            try {
                if (this.$t && typeof this.$t === 'function') {
                    return this.$t(key, params);
                }
                return fallback || key;
            } catch (error) {
                console.warn(`Translation error for key "${key}":`, error);
                return fallback || key;
            }
        },

        /**
         * 检查 i18n 是否可用
         * @returns {boolean}
         */
        $hasI18n() {
            return !!(this.$t && typeof this.$t === 'function');
        },

        /**
         * 获取当前语言
         * @returns {string}
         */
        $getCurrentLang() {
            return this.$i18n?.locale || 'zh-CN';
        }
    }
};
