#!/usr/bin/env node

/**
 * 自定义国际化文本扫描工具
 * 专门为 Vue + vue-i18n 项目设计
 * 扫描项目中所有需要国际化的中文文本
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class I18nScanner {
  constructor(options = {}) {
    this.options = {
      // 扫描的文件模式
      patterns: ['src/**/*.vue', 'src/**/*.js'],
      // 排除的文件模式
      exclude: ['**/node_modules/**', '**/*.test.js', '**/*.spec.js'],
      // 输出文件
      output: 'i18n-scan-report.json',
      // 是否输出详细信息
      verbose: true,
      ...options
    };
    
    this.results = {
      files: [],
      totalFiles: 0,
      totalMatches: 0,
      summary: {}
    };
  }

  // 检测中文字符的正则表达式
  getChinesePatterns() {
    return [
      // 模板中的中文文本 (排除已经使用 $t 的)
      {
        name: 'template-chinese',
        pattern: /(?<![\$\w])(['"`])([^'"`]*[\u4e00-\u9fa5][^'"`]*)(['"`])/g,
        exclude: /\$t\s*\(/
      },
      
      // JavaScript 字符串中的中文
      {
        name: 'js-string-chinese',
        pattern: /(['"`])([^'"`]*[\u4e00-\u9fa5][^'"`]*)(['"`])/g,
        exclude: /\$t\s*\(|console\.|\/\/|\/\*/
      },
      
      // 对象属性中的中文
      {
        name: 'object-property-chinese',
        pattern: /(title|label|placeholder|text|name|message)\s*:\s*(['"`])([^'"`]*[\u4e00-\u9fa5][^'"`]*)(['"`])/g
      },
      
      // HTML 标签内容中的中文
      {
        name: 'html-content-chinese',
        pattern: />([^<]*[\u4e00-\u9fa5][^<]*)</g,
        exclude: /\{\{|\$t\(/
      }
    ];
  }

  // 扫描单个文件
  scanFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const patterns = this.getChinesePatterns();
    const matches = [];

    patterns.forEach(({ name, pattern, exclude }) => {
      let match;
      const regex = new RegExp(pattern.source, pattern.flags);
      
      while ((match = regex.exec(content)) !== null) {
        const fullMatch = match[0];
        const chineseText = match[2] || match[1];
        
        // 排除特定模式
        if (exclude && exclude.test(fullMatch)) {
          continue;
        }
        
        // 排除纯数字、纯英文、特殊字符等
        if (!this.isValidChineseText(chineseText)) {
          continue;
        }
        
        const lineNumber = content.substring(0, match.index).split('\n').length;
        const lineContent = content.split('\n')[lineNumber - 1].trim();
        
        matches.push({
          type: name,
          text: chineseText,
          fullMatch: fullMatch,
          line: lineNumber,
          lineContent: lineContent,
          index: match.index
        });
      }
    });

    return matches;
  }

  // 验证是否为有效的中文文本
  isValidChineseText(text) {
    if (!text || text.length < 1) return false;
    
    // 排除纯数字、纯符号等
    if (!/[\u4e00-\u9fa5]/.test(text)) return false;
    
    // 排除常见的非翻译文本
    const excludePatterns = [
      /^[\d\s\-_\.]+$/,  // 纯数字、空格、连字符
      /^[a-zA-Z\s]+$/,   // 纯英文
      /^\w+$/,           // 单个单词
      /^[\W_]+$/,        // 纯符号
      /console\./,       // console 语句
      /import|export/,   // 导入导出语句
    ];
    
    return !excludePatterns.some(pattern => pattern.test(text));
  }

  // 扫描所有文件
  async scan() {
    console.log('🔍 开始扫描国际化文本...\n');
    
    const files = [];
    
    // 获取所有需要扫描的文件
    for (const pattern of this.options.patterns) {
      const matchedFiles = glob.sync(pattern, {
        ignore: this.options.exclude
      });
      files.push(...matchedFiles);
    }
    
    this.results.totalFiles = files.length;
    console.log(`📁 找到 ${files.length} 个文件需要扫描`);
    
    // 扫描每个文件
    for (const file of files) {
      if (this.options.verbose) {
        process.stdout.write(`扫描: ${file}...`);
      }
      
      try {
        const matches = this.scanFile(file);
        
        if (matches.length > 0) {
          this.results.files.push({
            path: file,
            matches: matches,
            count: matches.length
          });
          this.results.totalMatches += matches.length;
        }
        
        if (this.options.verbose) {
          console.log(` ✅ (${matches.length} 个匹配)`);
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(` ❌ 错误: ${error.message}`);
        }
      }
    }
    
    this.generateSummary();
    this.outputResults();
  }

  // 生成统计摘要
  generateSummary() {
    const summary = {
      totalFiles: this.results.totalFiles,
      filesWithMatches: this.results.files.length,
      totalMatches: this.results.totalMatches,
      byType: {},
      topFiles: []
    };
    
    // 按类型统计
    this.results.files.forEach(file => {
      file.matches.forEach(match => {
        summary.byType[match.type] = (summary.byType[match.type] || 0) + 1;
      });
    });
    
    // 找出匹配最多的文件
    summary.topFiles = this.results.files
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(file => ({
        path: file.path,
        count: file.count
      }));
    
    this.results.summary = summary;
  }

  // 输出结果
  outputResults() {
    console.log('\n📊 扫描完成！统计结果：');
    console.log(`总文件数: ${this.results.summary.totalFiles}`);
    console.log(`包含中文的文件: ${this.results.summary.filesWithMatches}`);
    console.log(`总匹配数: ${this.results.summary.totalMatches}`);
    
    console.log('\n📈 按类型统计:');
    Object.entries(this.results.summary.byType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\n🔥 需要重点关注的文件 (匹配数最多):');
    this.results.summary.topFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.path} (${file.count} 个匹配)`);
    });
    
    // 保存详细报告到文件
    fs.writeFileSync(this.options.output, JSON.stringify(this.results, null, 2));
    console.log(`\n💾 详细报告已保存到: ${this.options.output}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const scanner = new I18nScanner({
    patterns: ['src/**/*.vue', 'src/**/*.js'],
    exclude: ['**/node_modules/**', '**/locales/**', '**/*.test.js'],
    output: 'i18n-scan-report.json',
    verbose: true
  });
  
  scanner.scan().catch(console.error);
}

module.exports = I18nScanner;
