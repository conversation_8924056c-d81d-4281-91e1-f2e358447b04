# 国际化扫描工具使用指南

本文档介绍如何使用各种工具来扫描和统计项目中需要国际化的地方。

## 🛠️ 可用工具

### 1. 自定义扫描脚本 (推荐)

**文件**: `custom-i18n-scanner.js`

**功能**:
- 扫描 Vue 和 JS 文件中的中文文本
- 智能识别需要国际化的内容
- 生成详细的统计报告
- 按文件和类型分类统计

**使用方法**:
```bash
# 直接运行
npm run i18n:scan

# 或者
node custom-i18n-scanner.js
```

**输出文件**: `i18n-scan-report.json`

### 2. vue-i18n-extract

**文件**: `i18n-extract-config.js`

**功能**:
- 检测缺失的翻译键
- 检测未使用的翻译键
- 专门为 vue-i18n 设计

**使用方法**:
```bash
npm run i18n:extract
```

**输出文件**: `i18n-extract-report.json`

### 3. i18next-scanner

**文件**: `i18next-scanner-config.js`

**功能**:
- 通用的国际化扫描工具
- 支持多种框架
- 可自定义扫描规则

**使用方法**:
```bash
npm run i18n:check
```

### 4. i18n-ally (VSCode 插件)

**配置文件**: `.vscode/settings.json`

**功能**:
- 实时检测需要国际化的文本
- 可视化显示翻译状态
- 支持快速添加翻译键

**使用方法**:
1. 安装 VSCode 插件 "i18n Ally"
2. 插件会自动根据配置工作
3. 在编辑器中会高亮显示需要翻译的文本

## 📊 报告解读

### 自定义扫描报告结构

```json
{
  "files": [
    {
      "path": "src/views/example.vue",
      "matches": [
        {
          "type": "template-chinese",
          "text": "查询",
          "fullMatch": "'查询'",
          "line": 15,
          "lineContent": "<h-button>查询</h-button>",
          "index": 245
        }
      ],
      "count": 1
    }
  ],
  "totalFiles": 150,
  "totalMatches": 320,
  "summary": {
    "totalFiles": 150,
    "filesWithMatches": 45,
    "totalMatches": 320,
    "byType": {
      "template-chinese": 120,
      "js-string-chinese": 80,
      "object-property-chinese": 70,
      "html-content-chinese": 50
    },
    "topFiles": [...]
  }
}
```

### 匹配类型说明

- **template-chinese**: Vue 模板中的中文文本
- **js-string-chinese**: JavaScript 字符串中的中文
- **object-property-chinese**: 对象属性中的中文 (如 title, label 等)
- **html-content-chinese**: HTML 标签内容中的中文

## 🚀 快速开始

### 第一次使用

1. **安装依赖**:
```bash
npm install
```

2. **运行扫描**:
```bash
npm run i18n:scan
```

3. **查看报告**:
```bash
# 查看统计摘要 (控制台输出)
# 查看详细报告
cat i18n-scan-report.json
```

### 工作流程建议

1. **初始扫描**: 使用自定义扫描脚本获得全面的统计
2. **重点文件**: 根据报告中的 `topFiles` 优先处理匹配数最多的文件
3. **分类处理**: 按照匹配类型分别处理不同类型的文本
4. **验证检查**: 使用 vue-i18n-extract 检查翻译键的完整性

## 📝 最佳实践

### 1. 扫描前准备

- 确保代码已提交，避免扫描过程中的意外丢失
- 备份重要文件
- 了解项目的国际化结构

### 2. 处理优先级

1. **高优先级**: 用户界面文本 (按钮、标签、提示信息)
2. **中优先级**: 表格标题、表单字段
3. **低优先级**: 调试信息、开发者注释

### 3. 排除规则

以下内容通常不需要国际化:
- 技术术语 (API、URL、配置项)
- 数据库字段名
- 代码注释
- 调试信息
- 第三方库的内容

## 🔧 自定义配置

### 修改扫描规则

编辑 `custom-i18n-scanner.js` 中的 `getChinesePatterns()` 方法:

```javascript
// 添加新的匹配模式
{
  name: 'custom-pattern',
  pattern: /your-regex-here/g,
  exclude: /exclude-pattern/
}
```

### 修改排除规则

在 `isValidChineseText()` 方法中添加排除模式:

```javascript
const excludePatterns = [
  /your-exclude-pattern/,
  // ... 其他规则
];
```

## 🐛 常见问题

### Q: 扫描结果包含不需要翻译的内容
A: 修改 `isValidChineseText()` 方法中的排除规则

### Q: 某些文件没有被扫描到
A: 检查 `patterns` 配置，确保包含了正确的文件模式

### Q: 扫描速度太慢
A: 添加更多的排除规则，减少需要扫描的文件数量

### Q: 报告文件太大
A: 设置 `verbose: false` 减少详细信息的输出

## 📈 进度跟踪

建议创建一个进度跟踪表格:

| 文件路径 | 总匹配数 | 已处理 | 进度 | 负责人 | 备注 |
|---------|---------|--------|------|--------|------|
| src/views/monitor.vue | 25 | 10 | 40% | 张三 | 表格标题待处理 |
| src/components/header.vue | 15 | 15 | 100% | 李四 | 已完成 |

## 🎯 下一步

1. 运行扫描工具获取当前状态
2. 根据报告制定国际化计划
3. 按优先级逐步实施
4. 定期运行扫描验证进度
