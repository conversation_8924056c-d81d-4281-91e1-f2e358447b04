export default {
    // Common text
    common: {
        // Action buttons
        query: 'Query',
        reset: 'Reset',
        export: 'Export',
        delete: 'Delete',
        edit: 'Edit',
        detail: 'Detail',
        add: 'Add',
        save: 'Save',
        cancel: 'Cancel',
        confirm: 'Confirm',
        submit: 'Submit',
        close: 'Close',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        refresh: 'Refresh',
        search: 'Search',
        clear: 'Clear',
        select: 'Select',
        selectAll: 'Select All',

        // Status text
        success: 'Success',
        failed: 'Failed',
        error: 'Error',
        warning: 'Warning',
        info: 'Info',
        loading: 'Loading',
        running: 'Running',
        stopped: 'Stopped',
        paused: 'Paused',
        finished: 'Finished',
        pending: 'Pending',
        processing: 'Processing',

        // Common tips
        noData: 'No Data',
        pleaseSelect: 'Please Select',
        pleaseInput: 'Please Input',
        operationSuccess: 'Operation Success',
        operationFailed: 'Operation Failed',
        confirmDelete: 'Confirm to delete?',
        deleteSuccess: 'Delete Success',
        saveSuccess: 'Save Success',

        // Time related
        today: 'Today',
        yesterday: 'Yesterday',
        thisWeek: 'This Week',
        thisMonth: 'This Month',
        startTime: 'Start Time',
        endTime: 'End Time',
        timeRange: 'Time Range',

        // Form
        required: 'Required',
        optional: 'Optional',
        placeholder: {
            input: 'Please input',
            select: 'Please select',
            search: 'Please input search content'
        },
        // Validation related
        validation: {
            required: 'This field is required',
            email: 'Please enter a valid email address',
            phone: 'Please enter a valid phone number',
            url: 'Please enter a valid URL',
            number: 'Please enter a valid number',
            integer: 'Please enter an integer',
            positive: 'Please enter a positive number',
            range: 'Please enter a value between {min} and {max}',
            minLength: 'Please enter at least {min} characters',
            maxLength: 'Please enter no more than {max} characters'
        },

        // Error messages
        errorMsg: {
            networkError: 'Network Error',
            serverError: 'Server Error',
            timeout: 'Request Timeout',
            unauthorized: 'Unauthorized',
            forbidden: 'Forbidden',
            notFound: 'Not Found',
            internalError: 'Internal Error',
            unknownError: 'Unknown Error'
        }

    },

    // Navigation menu
    menu: {
        home: 'Home',

        // Product service config
        productService: {
            applicationLatency: 'Application Latency Measurement',
            linkModelConfig: 'Link Model Configuration',
            modelConfig: 'Model Configuration',
            modelPreview: 'Model Preview',
            linkLogConfig: 'Link Log Configuration',
            logSourceConfig: 'Link Log Configuration'
        }

    },

    // Page titles
    title: {
        sqlCores: 'SQL Core Query',
        managementQuery: 'Management Query',
        dataObservation: 'Data Observation'
    },

    // Config related
    config: {
        basic: 'Basic Config',
        advanced: 'Advanced Config',
        network: 'Network Config',
        database: 'Database Config',
        security: 'Security Config',
        performance: 'Performance Config'
    },

    // Business specific text
    business: {
        productInstance: 'Product Instance',
        coreInstance: 'Core Instance',
        dataSource: 'Data Source',
        cluster: 'Cluster',
        shard: 'Shard'
    },

    // Management function
    management: {

        label: {
            pluginName: 'Plugin Name',
            functionName: 'Function Name',
            chineseName: 'Chinese Name',
            remark: 'Remark',
            description: 'Description',
            version: 'Version',
            updateTime: 'Update Time',
            provider: 'Provider, default hs-ldp',
            accessUrl: 'Access URL',
            success: 'Success'
        },

        // 表格类文案（可选、business 代替）
        table: {},

        // Message tips
        message: {
            editorContentTooLarge: 'Editor content exceeds 1M and cannot be cached!',
            noManagementFunction: 'No management function found',
            showChineseName: 'Show "Chinese Name"',
            confirmSaveContent: 'Are you sure to save the current content in the editor?',
            fileNameRequired: 'File name cannot be empty',
            enterFileName: 'Please enter file name',
            supportDragSql: 'Support drag and drop SQL files'
        },

        // Form validation
        formValidation: {
            onlyFloat: 'Only float format allowed',
            onlyNonNegativeFloat: 'Only non-negative float format allowed',
            onlyNonPositiveFloat: 'Only non-positive float format allowed',
            invalidEmail: 'Invalid email address',
            onlyColor: 'Only color format allowed',
            onlyUrl: 'Only URL format allowed',
            onlyChinese: 'Only Chinese format allowed'
        },

        // Verify status
        verifyStatus: {
            notStarted: 'Not Started',
            running: 'Running',
            finished: 'Finished',
            paused: 'Paused',
            stopped: 'Stopped',
            failed: 'Failed',
            consistent: 'Consistent',
            inconsistent: 'Inconsistent'
        }
    }
};
