// i18next-scanner 配置文件
// 通用的国际化文本扫描工具

module.exports = {
    input: [
        'src/**/*.{js,vue}',
        // 排除不需要扫描的文件
        '!src/**/*.spec.{js,ts}',
        '!src/**/*.test.{js,ts}',
        '!**/node_modules/**'
    ],

    output: './',

    options: {
        debug: true,
        func: {
            // 扫描的函数名
            list: ['$t', 'this.$t', 'i18n.t'],
            extensions: ['.js', '.vue']
        },

        // 翻译键的命名空间
        lngs: ['zh-CN', 'en-US'],
        ns: ['translation'],
        defaultLng: 'zh-CN',
        defaultNs: 'translation',

        // 资源配置
        resource: {
            loadPath: 'src/locales/{{lng}}.js',
            savePath: 'i18n-extracted/{{lng}}.json',
            jsonIndent: 2,
            lineEnding: '\n'
        },

        // 移除未使用的键
        removeUnusedKeys: false,

        // 排序键
        sort: true,

        // 自定义转换函数
        transform: function(file, enc, done) {

            const parser = this.parser;
            const content = file.contents.toString();

            // 自定义正则表达式匹配
            const patterns = [
                // 匹配 $t('key') 或 $t("key")
                /\$t\s*\(\s*['"`]([^'"`]+)['"`]/g,
                // 匹配 this.$t('key')
                /this\.\$t\s*\(\s*['"`]([^'"`]+)['"`]/g,
                // 匹配模板中的 {{ $t('key') }}
                /\{\{\s*\$t\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\}\}/g,
                // 匹配 v-bind 中的 :placeholder="$t('key')"
                /:[\w-]+\s*=\s*["`]\s*\$t\s*\(\s*['"`]([^'"`]+)['"`]/g
            ];

            patterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(content)) !== null) {
                    const key = match[1];
                    if (key) {
                        parser.set(key, {
                            defaultValue: key,
                            context: {
                                file: file.path,
                                line: content.substring(0, match.index).split('\n').length
                            }
                        });
                    }
                }
            });

            done();
        }
    }
};
